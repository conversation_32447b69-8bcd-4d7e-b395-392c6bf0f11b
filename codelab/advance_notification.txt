Write Your eBook

Section 8 eBook
Section Title
Enhancing Notifications with Advanced Features



Chapter Title 
Section Overview 


[H2] Section Overview
[T] In this section, you'll explore advanced notification features that take your app's user experience to the next level. Just as an actor uses different techniques to convey different types of messages, your app can use these advanced features to create more engaging and informative notifications.

[T] In this section, you'll explore:
[List]
Display notifications with progress indicators for tasks like downloads or uploads
Group multiple notifications into stacks to reduce notification drawer clutter
Add interactive actions like buttons to let users respond directly from notifications
Display media-rich notifications with images to provide visual context

[T] These advanced features will help your notifications stand out and provide users with more ways to interact with your app without even opening it. Let's dive in and explore these powerful capabilities!



Next Chapter Title 
Implementing Progress Notifications and Grouped Notifications

[H2] Implementing Progress Notifications
[T] In this chapter, we'll implement progress notifications that show real-time updates for ongoing operations like downloads, uploads, or processing tasks. Think of these as the digital equivalent of a loading bar or progress meter that keeps users informed about lengthy operations.
[H3] Understanding Progress Notifications
[T] Progress notifications are particularly useful for:
[List]
File downloads or uploads
Data synchronization operations
Installation or update processes
Long-running background tasks

[T] On Android, progress notifications can display an actual progress bar, while on iOS (which doesn't natively support progress bars in notifications), we'll update the notification text to indicate progress.
[H3] Enhancing the NotificationModel
[T] Our NotificationModel already includes properties for tracking progress:

[Code: dart]
class NotificationModel {
  // Other properties...
  final int? maxProgress;
  final int? currentProgress;
  
  // Constructor and other methods...
}

[T] These properties allow us to store the maximum value and current value of the progress, which we'll use to calculate the percentage and display the progress bar.
[H3] Implementing the Progress Notification Method
[T] Let's implement the showProgressNotification method in the NotificationManager class:

[Code: dart]
/// Shows a notification with a progress indicator.
///
/// Displays a notification showing progress toward completion of a task,
/// using the values in [model.currentProgress] and [model.maxProgress].
///
/// Throws an [ArgumentError] if progress values are not provided.
Future<void> showProgressNotification({
  required NotificationModel model,
}) async {
  if (model.maxProgress == null || model.currentProgress == null) {
    throw ArgumentError(
      'maxProgress and currentProgress must be provided for progress notifications',
    );
  }

  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show progress notification: prerequisites not met');
    return;
  }

  AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
    model.channelId,
    model.channelId.toUpperCase(),
    importance: Importance.low,
    priority: Priority.low,
    onlyAlertOnce: true,
    showProgress: true,
    maxProgress: model.maxProgress!,
    progress: model.currentProgress!,
  );

  DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
    categoryIdentifier: model.channelId,
    presentAlert: true,
    presentBadge: true,
    presentSound: false,
  );

  NotificationDetails details = NotificationDetails(
    android: androidDetails,
    iOS: iosDetails,
  );

  await _flutterLocalNotificationsPlugin.show(
    model.id,
    model.title,
    model.body,
    details,
    payload: model.toPayload(),
  );
}

[T] This method configures a notification with a progress bar on Android. Notice a few important settings:
[List]
importance: Importance.low and priority: Priority.low - Progress notifications are typically less intrusive
onlyAlertOnce: true - This prevents the notification from making a sound every time it updates
showProgress: true - This enables the progress bar on Android
maxProgress and progress - These set the maximum value and current value of the progress bar
[H3] Enhancing the NotificationBuilder
[T] Now, let's enhance the NotificationBuilder to make it easier to create progress notifications:

[Code: dart]
/// Configures the notification to display a progress indicator.
///
/// Shows a progress bar in the notification, useful for download or
/// processing tasks.
///
/// [current] Current progress value
/// [max] Maximum progress value
///
/// Throws an [ArgumentError] if values are invalid.
NotificationBuilder setProgress(int current, int max) {
  if (current < 0) {
    throw ArgumentError('Current progress cannot be negative');
  }
  if (max <= 0) {
    throw ArgumentError('Maximum progress must be positive');
  }
  if (current > max) {
    throw ArgumentError('Current progress cannot exceed maximum');
  }
  _model = _model.copyWith(currentProgress: current, maxProgress: max);
  return this;
}

[T] This method validates the progress values and updates the notification model. It ensures that the progress values make sense (non-negative current, positive maximum, current not exceeding maximum).
[H3] Creating a Progress Notification
[T] Let's create a helper method in our controller to create progress notifications:

[Code: dart]
// Progress notification setup
NotificationBuilder createProgressNotification({
  required int progress,
  required int maxProgress,
}) {
  final id = DateTime.now().millisecondsSinceEpoch % 10000;
  final bool isComplete = progress >= maxProgress;

  final title = isComplete ? 'Download Complete' : 'Download Progress';
  final body =
      isComplete
          ? 'File ready to open'
          : 'Downloading: ${(progress / maxProgress * 100).round()}%';

  return notificationManager
      .createNotification(
        id: id,
        title: title,
        body: body,
        channelId: value.channelId,
        level: NotificationLevel.normal,
      )
      .setProgress(progress, maxProgress);
}

[T] This method creates a notification with a title and body that reflect the current progress. It also sets the progress values using the setProgress method.
[H3] Updating Progress
[T] To update a progress notification, we simply show a new notification with the same ID but updated progress values:

[Code: dart]
// Example of updating progress
Future<void> updateDownloadProgress() async {
  int maxProgress = 100;
  
  // Initial notification
  var builder = createProgressNotification(progress: 0, maxProgress: maxProgress);
  await builder.show();
  
  // Simulate progress updates
  for (int i = 10; i <= maxProgress; i += 10) {
    await Future.delayed(const Duration(seconds: 1));
    builder = createProgressNotification(progress: i, maxProgress: maxProgress);
    await builder.show();
  }
}

[T] This example simulates a download that takes 10 seconds to complete, updating the progress every second. In a real app, you would update the progress based on actual download progress events.

[T] Preview
[Video]
progress-demo.mp4

[T] Progress notifications are a powerful way to keep users informed about ongoing operations. They provide visual feedback that helps users understand what's happening and how long they might need to wait.

[T] We'll now implement grouped notifications, which allow you to bundle related notifications together to reduce clutter in the notification drawer. Think of this like organizing related emails into a conversation thread instead of showing each message separately.
[H3] Understanding Notification Groups
[T] Notification groups are particularly useful for:
[List]
Messaging apps that might receive multiple messages from the same conversation
Social media apps that receive multiple interactions (likes, comments, etc.)
News apps that receive multiple updates on related topics
Any app that might generate multiple related notifications in a short time

[T] On Android, grouped notifications appear as a single expandable item with a summary, while on iOS, they appear as a stack that users can swipe through.
[H3] Implementing the Group Notification Method
[T] Let's implement the showGroupedNotifications method in the NotificationManager class:

[Code: dart]
/// Shows a group of related notifications with a summary.
///
/// Creates individual notifications for each item in [notifications] and
/// a summary notification that groups them together (Android only).
///
/// [groupKey] A unique identifier for this group of notifications
/// [groupChannelId] The channel ID for the notifications
/// [summaryTitle] The title for the summary notification
/// [summaryBody] The body text for the summary notification
Future<void> showGroupedNotifications({
  required String groupKey,
  required String groupChannelId,
  required String summaryTitle,
  required String summaryBody,
  required List<NotificationModel> notifications,
}) async {
  final canShow = await _checkNotificationPrerequisites();
  if (!canShow) {
    debugPrint('Cannot show grouped notifications: prerequisites not met');
    return;
  }

  // Create individual notifications
  for (final notification in notifications) {
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      groupChannelId,
      groupChannelId.toUpperCase(),
      importance: Importance.high,
      priority: Priority.high,
      groupKey: groupKey,
      setAsGroupSummary: false,
    );

    DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      categoryIdentifier: groupChannelId,
      threadIdentifier: groupKey,
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      notification.id,
      notification.title,
      notification.body,
      details,
      payload: notification.toPayload(),
    );
  }

  // Create summary notification for Android
  if (Platform.isAndroid) {
    AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
      groupChannelId,
      groupChannelId.toUpperCase(),
      importance: Importance.high,
      priority: Priority.high,
      groupKey: groupKey,
      setAsGroupSummary: true,
      styleInformation: InboxStyleInformation(
        notifications.map((n) => n.body).toList(),
        contentTitle: summaryTitle,
        summaryText: '${notifications.length} messages',
      ),
    );

    NotificationDetails details = NotificationDetails(
      android: androidDetails,
    );

    await _flutterLocalNotificationsPlugin.show(
      0, // Use a fixed ID for the summary
      summaryTitle,
      summaryBody,
      details,
    );
  }
}

[T] This method does two important things:
[List]
Creates individual notifications for each item in the notifications list, with groupKey for Android and threadIdentifier for iOS to link them together
Creates a summary notification for Android that displays a count and summary of the individual notifications

[H3] Creating a Helper Method for Group Notifications
[T] Let's create a helper method to make it easier to create grouped notifications:

[Code: dart]
/// Creates a group of notifications with a summary.
///
/// Converts the list of [notifications] to models and calls
/// [showGroupedNotifications] to display them.
Future<void> createGroupNotification({
  required String groupKey,
  required String channelId,
  required String groupTitle,
  required String groupSummary,
  required List<NotificationBuilder> notifications,
}) async {
  List<NotificationModel> models =
      notifications.map((builder) => builder.model).toList();
  return await showGroupedNotifications(
    groupKey: groupKey,
    groupChannelId: channelId,
    summaryTitle: groupTitle,
    summaryBody: groupSummary,
    notifications: models,
  );
}

[T] This method takes a list of NotificationBuilder instances, extracts their models, and passes them to the showGroupedNotifications method.

[T] Preview
[Video]
group-demo.mp4

[T] Grouped notifications are a powerful way to organize related notifications and reduce clutter in the notification drawer. They provide a better user experience by showing a summary of related notifications instead of individual alerts.






Next Chapter Title 
Implementing Interactive and Media-Rich Notifications


[H2] Implementing Interactive and Media Rich Notifications
[T] In this chapter, we'll implement interactive notifications that allow users to take actions directly from the notification without opening the app. Think of these as mini-interfaces that extend your app's functionality into the notification drawer.
[H3] Understanding Interactive Notifications
[T] Interactive notifications are particularly useful for:
[List]
Responding to messages without opening the messaging app
Snoozing or dismissing reminders
Accepting or declining invitations
Completing simple tasks like marking items as done

[T] Both Android and iOS support interactive notifications, but they implement them differently. On Android, actions appear as buttons below the notification, while on iOS, they appear when the user long-presses on the notification.
[H3] Setting Up Notification Actions
[T] First, let's set up the constants for our notification actions:

[Code: dart]
/// Notification action IDs
class NotificationActionIds {
  static const String snooze = 'snooze';
  static const String dismiss = 'dismiss';
  static const String reply = 'reply';
}

/// Notification action texts
class NotificationActionTexts {
  static const String snooze = 'Snooze';
  static const String dismiss = 'Dismiss';
  static const String reply = 'Reply';
}

/// Notification categories
class NotificationCategories {
  static const String interactive = 'INTERACTIVE_CATEGORY';
}

[T] These constants define the IDs and display texts for our notification actions, as well as a category for interactive notifications.
[H3] Setting Up iOS Notification Categories
[T] For iOS, we need to set up notification categories with actions. Let's update the _initializeNotificationSettings method:

[Code: dart]
/// Initializes notification settings and categories.
///
/// Configures platform-specific settings and action categories, especially
/// for interactive notifications.
Future<void> _initializeNotificationSettings() async {
  final androidSettings = AndroidInitializationSettings(
    NotificationResources.defaultIcon,
  );

  // Configure iOS action categories
  final snoozeAction = DarwinNotificationAction.plain(
    NotificationActionIds.snooze,
    NotificationActionTexts.snooze,
    options: {DarwinNotificationActionOption.foreground},
  );
  final dismissAction = DarwinNotificationAction.plain(
    NotificationActionIds.dismiss,
    NotificationActionTexts.dismiss,
    options: {DarwinNotificationActionOption.foreground},
  );
  final replyAction = DarwinNotificationAction.text(
    NotificationActionIds.reply,
    NotificationActionTexts.reply,
    buttonTitle: NotificationActionTexts.reply,
  );

  final interactiveCategory = DarwinNotificationCategory(
    NotificationCategories.interactive,
    actions: [snoozeAction, dismissAction, replyAction],
  );

  final iosSettings = DarwinInitializationSettings(
    requestSoundPermission: true,
    requestBadgePermission: true,
    requestAlertPermission: true,
    notificationCategories: [interactiveCategory],
  );

  final initializationSettings = InitializationSettings(
    android: androidSettings,
    iOS: iosSettings,
  );

  await _flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    onDidReceiveNotificationResponse: notificationTapBackground,
  );
}

[T] This method sets up an interactive category for iOS with three actions: snooze, dismiss, and reply. The reply action includes a text input field for the user to enter a response.
[H3] Updating the Notification Details Configuration
[T] Now, let's update the getNotificationDetailsConfig method to include actions for Android:

[Code: dart]
// Configure Android specific details
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  styleInformation:
      imageAttachment
          ? BigPictureStyleInformation(
            ByteArrayAndroidBitmap(imageBytes),
            largeIcon: ByteArrayAndroidBitmap(imageBytes),
          )
          : null,
  actions: hasActions
      ? [
          const AndroidNotificationAction(
            NotificationActionIds.snooze,
            NotificationActionTexts.snooze,
          ),
          const AndroidNotificationAction(
            NotificationActionIds.dismiss,
            NotificationActionTexts.dismiss,
            cancelNotification: true,
          ),
          const AndroidNotificationAction(
            NotificationActionIds.reply,
            NotificationActionTexts.reply,
            inputs: [
              AndroidNotificationActionInput(
                label: NotificationActionTexts.reply,
                allowFreeFormInput: true,
              ),
            ],
          ),
        ]
      : null,
);

// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  attachments:
      imageAttachment
          ? [
            DarwinNotificationAttachment(
              filePath,
              thumbnailClippingRect:
                  const DarwinNotificationAttachmentThumbnailClippingRect(
                    x: 0.0,
                    y: 0.0,
                    width: 0.3,
                    height: 0.3,
                  ),
            ),
          ]
          : null,
);

[T] This code adds action buttons to Android notifications and sets the appropriate category for iOS notifications. For Android, we add "Snooze", "Dismiss", and "Reply" buttons, with the "Dismiss" button configured to automatically cancel the notification when tapped.
[H3] Enhancing the NotificationBuilder
[T] Let's enhance the NotificationBuilder to make it easier to create interactive notifications:

[Code: dart]
/// Adds interactive action buttons to the notification.
///
NotificationBuilder setActions() {
  _model = _model.copyWith(hasActions: true);
  return this;
}

[T] This method sets the hasActions flag on the notification model, which will cause the notification to include action buttons when displayed.
[H3] Handling Action Responses
[T] When a user taps an action button, we need to handle the response. Let's update the notificationTapBackground callback:

[Code: dart]
/// Callback handler for notification actions when the app is in the background.
@pragma('vm:entry-point')
void notificationTapBackground(NotificationResponse notificationResponse) {
  debugPrint(
    'notification(${notificationResponse.id}) action tapped: '
    '${notificationResponse.actionId} with'
    ' payload: ${notificationResponse.payload}',
  );
  if (notificationResponse.input?.isNotEmpty ?? false) {
    debugPrint(
      'notification action tapped with input: ${notificationResponse.input}',
    );
  }

  // Handle notification tap by navigating to the appropriate screen
  if (notificationResponse.payload != null) {
    try {
      final notificationModel = NotificationModel.fromPayload(
        notificationResponse.payload!,
      );

      // Handle specific actions
      if (notificationResponse.actionId != null) {
        _handleNotificationAction(
          notificationResponse.actionId!,
          notificationModel,
          notificationResponse.input,
        );
        return;
      }

      // If no specific action, handle the notification tap
      // If the notification has a deeplink, use it
      if (notificationModel.deepLink != null) {
        debugPrint(
          'Notification tapped with deeplink: ${notificationModel.deepLink}',
        );
        // Use the DeepLinkHandler to process the deeplink
        DeepLinkHandler.instance.handleNotificationDeeplink(
          notificationModel.deepLink,
        );
      } else {
        // Fallback to direct navigation if no deeplink is provided
        debugPrint(
          'Notification tapped without deeplink, using ID: ${notificationModel.id}',
        );
        AppRouter.navigateTo(
          AppRoutes.notificationDetails.value,
          arguments: notificationModel.id,
        );
      }
    } catch (e) {
      debugPrint('Error processing notification payload: $e');
    }
  }
}

/// Handles notification action taps.
///
/// Processes different actions based on the actionId.
void _handleNotificationAction(
  String actionId,
  NotificationModel model,
  String? input,
) {
  switch (actionId) {
    case NotificationActionIds.snooze:
      _handleSnoozeAction(model);
      break;
    case NotificationActionIds.dismiss:
      _handleDismissAction(model);
      break;
    case NotificationActionIds.reply:
      _handleReplyAction(model, input);
      break;
    default:
      debugPrint('Unknown action: $actionId');
  }
}

/// Handles the snooze action.
///
/// Reschedules the notification for a later time.
void _handleSnoozeAction(NotificationModel model) {
  debugPrint('Handling snooze action for notification ${model.id}');
  // In a real app, you would reschedule the notification for a later time
  // For this example, we'll just log the action
}

/// Handles the dismiss action.
///
/// Cancels the notification.
void _handleDismissAction(NotificationModel model) {
  debugPrint('Handling dismiss action for notification ${model.id}');
  // The notification is automatically canceled by the system when the dismiss action is tapped
  // with cancelNotification: true, but we could perform additional cleanup here
}

/// Handles the reply action.
///
/// Processes the user's reply text.
void _handleReplyAction(NotificationModel model, String? input) {
  debugPrint('Handling reply action for notification ${model.id}');
  if (input != null && input.isNotEmpty) {
    debugPrint('Reply text: $input');
    // In a real app, you would send the reply to the appropriate destination
    // For this example, we'll just log the reply
  }
}

[T] This code handles different notification actions based on the actionId. It includes separate methods for handling snooze, dismiss, and reply actions, which you can customize based on your app's needs.

[T] Preview
[Image]


[T] Interactive notifications are a powerful way to extend your app's functionality into the notification drawer. They allow users to take actions without opening the app, providing a more seamless and efficient user experience.

[T] We'll now look at how to implement media-rich notifications that include images to provide visual context. Think of these as enhanced notifications that catch the user's eye and convey information more effectively through visuals.
[H3] Understanding Media-Rich Notifications
[T] Media-rich notifications are particularly useful for:
[List]
Showing a preview of a received image
Displaying a map for location-based notifications
Showing a product image for e-commerce notifications
Enhancing news or social media notifications with relevant visuals

[T] Both Android and iOS support media-rich notifications, but they implement them differently. On Android, images are displayed using BigPictureStyleInformation, while on iOS, they are displayed using DarwinNotificationAttachment.
[H3] Preparing Images for Notifications
[T] To use images in notifications, we need to prepare them in the appropriate format. For simplicity, we'll use a sample image encoded as base64:

[Code: dart]
final Uint8List imageBytes = base64Decode(
  NotificationUtils.sampleImageBase64,
);

// Get the temporary directory using path_provider
final Directory tempDir = await getTemporaryDirectory();
final String filePath = '${tempDir.path}/notification_image.png';
final File imageFile = File(filePath);

// Only write the file if it doesn't already exist
if (!await imageFile.exists()) {
  await imageFile.writeAsBytes(imageBytes);
}

[T] This code decodes a base64-encoded image and saves it to a temporary file, which we'll use for the notification. In a real app, you might download an image from a URL or use an image from the app's assets.

[H3] Updating the Notification Details Configuration
[T] Now, let's update the getNotificationDetailsConfig method to include images:

[Code: dart]
// Configure Android specific details
AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
  effectiveChannelId,
  effectiveChannelId.toUpperCase(),
  importance: level.importance,
  priority: level.priority,
  playSound: level.playSound,
  enableVibration: level.vibrate,
  visibility: level.visibility,
  fullScreenIntent: isFullScreen,
  styleInformation:
      imageAttachment
          ? BigPictureStyleInformation(
            ByteArrayAndroidBitmap(imageBytes),
            largeIcon: ByteArrayAndroidBitmap(imageBytes),
          )
          : null,
  // Other settings...
);

// Configure iOS specific details
DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
  categoryIdentifier: hasActions ? NotificationCategories.interactive : channelId,
  presentAlert: true,
  presentBadge: true,
  presentSound: level.playSound,
  sound: customSound ? NotificationResources.customSoundIOS : null,
  attachments:
      imageAttachment
          ? [
            DarwinNotificationAttachment(
              filePath,
              thumbnailClippingRect:
                  const DarwinNotificationAttachmentThumbnailClippingRect(
                    x: 0.0,
                    y: 0.0,
                    width: 0.3,
                    height: 0.3,
                  ),
            ),
          ]
          : null,
  // Other settings...
);

[T] This code adds an image to the notification if the imageAttachment flag is set. For Android, it uses BigPictureStyleInformation with the image as both the main image and the large icon. For iOS, it uses DarwinNotificationAttachment with the image file path.
[H3] Enhancing the NotificationBuilder
[T] Let's enhance the NotificationBuilder to make it easier to create media-rich notifications:

[Code: dart]
/// Adds an image to be displayed in the expanded notification view.
NotificationBuilder setImage(bool imageAttachment) {
  _model = _model.copyWith(imageAttachment: imageAttachment);
  return this;
}

[T] This method sets the imageAttachment flag on the notification model, which will cause the notification to include an image when displayed.

[T] Media-rich notifications are a powerful way to enhance your notifications with visual content. They catch the user's eye and convey information more effectively through visuals, providing a more engaging user experience.

[Video]
image-demo.mov

[T] Run the App: Simulate and test each notification type on an emulator or device.

[T] Up Next: Let's summarize what we've learned in this section and look ahead to the next section.
Last Chapter Title 
Section Summary 


[H2] Section Summary
[T] In this section, we've explored advanced notification features that enhance the user experience and provide more ways for users to interact with your app:
[List]
Progress Notifications: To provide real-time updates for ongoing processes, such as downloads or uploads, we implemented notifications with progress bars.
Grouped Notifications: In order to clear up the notification drawer, we put in place notification groups that combine related notifications into one bundle.
Interactive Notifications: To enable users to take action straight from the notification without launching the app, we added action buttons to the notifications.
Media-Rich Notifications: To add visual context and increase engagement, we used images in our notifications.

[T] These advanced features take your notifications to the next level, providing a more engaging and interactive user experience. They allow users to stay informed and take actions without even opening your app, making your app more useful and user-friendly.
[H3] Key Takeaways
[List]
Progress notifications keep users informed about ongoing operations with visual feedback.
Grouped notifications reduce clutter in the notification drawer by bundling related notifications together.
Interactive notifications extend your app's functionality into the notification drawer, allowing users to take actions without opening the app.
Media-rich notifications enhance your notifications with visual content, making them more engaging and informative.
[T] By implementing these advanced features, you can create a more engaging and interactive notification system that enhances user experience, increases user retention, and makes your app stand out from the competition.

[T] Up next, we will explore debugging techniques which can be used for flutter_local_notifications.




